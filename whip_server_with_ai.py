#!/usr/bin/env python3
import argparse
import asyncio
import json
import logging
import os
import ssl
import uuid
import signal
import sys


# 修改导入名称
from aiohttp import web
import aiohttp_cors  # 使用pip安装的aiohttp-cors包
from aiortc import MediaStreamTrack, RTCPeerConnection, RTCSessionDescription, RTCConfiguration
from aiortc.contrib.media import MediaRelay
from aiortc.rtcicetransport import R<PERSON><PERSON>ceGatherer, candidate_from_aioice
from av import VideoFrame

import cv2
import numpy as np
import re
import fractions
from typing import List, Tuple
from low_latency_config import get_low_latency_config, apply_server_optimizations

# 导入配置
from config import get_stun_turn_config, reload_config

# 配置日志（需要在ICE修复之前）
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("whip_server_ai")

# 在服务器启动时重新加载配置，确保环境变量生效
def load_env_variables():
    """加载.env文件中的环境变量"""
    import os
    from pathlib import Path

    env_path = Path(".env")
    if env_path.exists():
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        # 移除引号
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]

                        os.environ[key] = value
                        logger.info(f"✅ 设置环境变量: {key}")

            logger.info("✅ 成功加载.env文件")
            # 重新加载配置
            reload_config()
            return True
        except Exception as e:
            logger.error(f"❌ 加载.env文件失败: {e}")
            return False
    else:
        logger.warning("⚠️ .env文件不存在")
        return False

# 立即加载环境变量
logger.info("🔧 开始加载环境变量...")
load_env_variables()

# ICE候选项SDP修复补丁
_original_gather = RTCIceGatherer.gather
_original_createDtlsTransport = RTCPeerConnection._RTCPeerConnection__createDtlsTransport
_original_localDescription = RTCPeerConnection.localDescription

class ICESDPFixer:
    """ICE候选项SDP修复器"""

    def __init__(self):
        self.pc_candidates_map = {}  # PeerConnection -> 候选项列表的映射

    def _inject_candidates_to_sdp(self, sdp, candidates):
        """将候选项注入到SDP中"""
        lines = sdp.split('\n')
        new_lines = []

        # 找到媒体段
        in_media_section = False
        media_section_processed = False

        for line in lines:
            new_lines.append(line)

            # 检测媒体段开始
            if line.startswith('m='):
                in_media_section = True
                media_section_processed = False

            # 在媒体段中，在ice-pwd行后添加候选项
            if in_media_section and line.startswith('a=ice-pwd:') and not media_section_processed:
                # 添加候选项行
                for candidate in candidates:
                    candidate_line = self._candidate_to_sdp_line(candidate)
                    if candidate_line:
                        new_lines.append(candidate_line)
                        logger.debug(f"📝 添加候选项到SDP: {candidate_line}")

                media_section_processed = True

        return '\n'.join(new_lines)

    def _candidate_to_sdp_line(self, candidate):
        """将RTCIceCandidate转换为SDP候选项行"""
        try:
            # 构造SDP候选项行
            foundation = getattr(candidate, 'foundation', '1')
            component = getattr(candidate, 'component', 1)
            protocol = getattr(candidate, 'protocol', 'udp').upper()
            priority = getattr(candidate, 'priority', 2130706431)
            address = getattr(candidate, 'ip', candidate.address if hasattr(candidate, 'address') else '')
            port = getattr(candidate, 'port', 0)
            typ = getattr(candidate, 'type', 'host')

            candidate_line = f"a=candidate:{foundation} {component} {protocol} {priority} {address} {port} typ {typ}"

            # 添加相关地址信息（对于srflx和relay类型）
            if hasattr(candidate, 'relatedAddress') and candidate.relatedAddress:
                candidate_line += f" raddr {candidate.relatedAddress}"
            if hasattr(candidate, 'relatedPort') and candidate.relatedPort:
                candidate_line += f" rport {candidate.relatedPort}"

            return candidate_line

        except Exception as e:
            logger.error(f"❌ 转换候选项到SDP行失败: {e}")
            return None

# 创建全局修复器实例
ice_sdp_fixer = ICESDPFixer()

async def patched_gather(gatherer_self):
    """修复的gather方法，正确触发候选项事件并存储到SDP修复器"""
    if gatherer_self._RTCIceGatherer__state == "new":
        gatherer_self._RTCIceGatherer__setState("gathering")

        # 记录收集前的候选项数量
        initial_count = len(gatherer_self._connection.local_candidates)
        logger.debug(f"开始ICE收集，当前候选项数量: {initial_count}")

        # 执行原始的候选项收集
        await gatherer_self._connection.gather_candidates()

        # 检查新增的候选项
        current_candidates = gatherer_self._connection.local_candidates
        new_candidates = current_candidates[initial_count:]

        logger.debug(f"ICE收集完成: 初始={initial_count}, 当前={len(current_candidates)}, 新增={len(new_candidates)}")

        # 存储候选项到修复器
        pc = getattr(gatherer_self, '_peer_connection', None)
        if pc:
            if pc not in ice_sdp_fixer.pc_candidates_map:
                ice_sdp_fixer.pc_candidates_map[pc] = []

            # 转换并存储候选项
            for aioice_candidate in new_candidates:
                try:
                    rtc_candidate = candidate_from_aioice(aioice_candidate)
                    ice_sdp_fixer.pc_candidates_map[pc].append(rtc_candidate)
                    logger.debug(f"✅ 存储候选项: {rtc_candidate.type} {rtc_candidate.ip}:{rtc_candidate.port}")

                    # 触发事件
                    pc.emit("icecandidate", rtc_candidate)
                except Exception as e:
                    logger.error(f"❌ 转换候选项失败: {e}")

            # 触发收集完成事件
            pc.emit("icecandidate", None)

        gatherer_self._RTCIceGatherer__setState("completed")

def patched_createDtlsTransport(self):
    """修复的createDtlsTransport方法，建立PeerConnection和ICE收集器的关联"""
    # 调用原始方法
    dtlsTransport = _original_createDtlsTransport(self)

    # 建立关联
    iceGatherer = dtlsTransport.transport.iceGatherer
    iceGatherer._peer_connection = self

    logger.debug(f"建立PeerConnection和ICE收集器的关联")

    return dtlsTransport

def patched_localDescription(pc_self):
    """修复的localDescription属性，确保SDP包含候选项"""
    original_desc = _original_localDescription.fget(pc_self)

    if original_desc and pc_self in ice_sdp_fixer.pc_candidates_map:
        candidates = ice_sdp_fixer.pc_candidates_map[pc_self]
        if candidates:
            # 修复SDP，添加候选项
            fixed_sdp = ice_sdp_fixer._inject_candidates_to_sdp(original_desc.sdp, candidates)
            if fixed_sdp != original_desc.sdp:
                logger.info(f"🔧 SDP已修复，添加了 {len(candidates)} 个候选项")
                # 创建新的描述对象
                from aiortc.rtcsessiondescription import RTCSessionDescription
                return RTCSessionDescription(sdp=fixed_sdp, type=original_desc.type)

    return original_desc

# 应用ICE候选项SDP修复补丁
RTCIceGatherer.gather = patched_gather
RTCPeerConnection._RTCPeerConnection__createDtlsTransport = patched_createDtlsTransport
RTCPeerConnection.localDescription = property(patched_localDescription)

# AI Model Configuration
MODEL_PATH = "models/yolo11l_pose.onnx"
DEVICE = "cuda" if os.path.exists("/dev/nvidia0") else "cpu"

# Server Configuration
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 8090
WHIP_ENDPOINT = "/whip"
WHEP_ENDPOINT = "/whep"

# YOLO模型加载（全局加载一次）
_yolo_model = None

def load_yolo_model():
    """Load YOLO pose detection model once"""
    global _yolo_model
    if _yolo_model is None:
        try:
            import onnxruntime as ort
            import os
            
            if os.path.exists(MODEL_PATH):
                providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if DEVICE == 'cuda' else ['CPUExecutionProvider']
                _yolo_model = ort.InferenceSession(MODEL_PATH, providers=providers)
                logger.info(f"YOLO model loaded successfully: {MODEL_PATH}")
            else:
                logger.warning(f"YOLO model not found: {MODEL_PATH}")
        except Exception as e:
            logger.error(f"Failed to load YOLO model: {e}")
    return _yolo_model

# 日志已在上面配置

# 全局变量
relay = MediaRelay()  # 用于转发媒体流
peer_connections = {}  # 存储所有的对等连接
streams = {}  # 存储所有的流，键为流ID，值为MediaStreamTrack
# ai_processors removed - YOLO is now integrated directly into VideoTransformTrack
pcs = set()  # Simple peer connection set for example compatibility
connection_info_shown = set()  # 防止重复显示连接信息

# ICE日志解析器
class ICELogParser:
    """解析ICE日志以提取连接信息"""

    def __init__(self):
        self.recent_logs = []  # 存储最近的日志
        self.max_logs = 100   # 最多保存100条日志

    def add_log(self, log_message):
        """添加日志消息"""
        self.recent_logs.append(log_message)
        if len(self.recent_logs) > self.max_logs:
            self.recent_logs.pop(0)

    def parse_successful_connection(self):
        """解析最近成功的ICE连接"""
        # 查找最近的成功连接日志
        # 格式: "Check CandidatePair(('***********', 56548) -> ('***********', 46303)) State.IN_PROGRESS -> State.SUCCEEDED"
        pattern = r"Check CandidatePair\(\('([^']+)', (\d+)\) -> \('([^']+)', (\d+)\)\) State\.IN_PROGRESS -> State\.SUCCEEDED"

        # 查看所有日志，不限制数量
        all_logs = self.recent_logs

        # 从最新的日志开始查找，选择最近的成功连接
        for log_msg in reversed(all_logs):
            match = re.search(pattern, log_msg)
            if match:
                local_ip = match.group(1)
                local_port = int(match.group(2))
                remote_ip = match.group(3)
                remote_port = int(match.group(4))

                # 分析连接类型
                connection_type = self._analyze_connection_type(local_ip, remote_ip)

                logger.debug(f"🔍 ICE日志解析成功: {local_ip}:{local_port} -> {remote_ip}:{remote_port}")
                logger.debug(f"🔍 连接类型: {connection_type}")
                logger.debug(f"🔍 原始日志: {log_msg}")

                return {
                    'local_ip': local_ip,
                    'local_port': local_port,
                    'remote_ip': remote_ip,
                    'remote_port': remote_port,
                    'connection_type': connection_type,
                    'raw_log': log_msg
                }

        # 如果没有找到，输出调试信息
        logger.debug(f"🔍 ICE日志解析失败，总日志数: {len(all_logs)}")
        logger.debug(f"🔍 最近5条日志:")
        for i, log_msg in enumerate(all_logs[-5:]):
            logger.debug(f"🔍   {i+1}: {log_msg}")

        return None

    def clear_logs(self):
        """清理日志缓存"""
        self.recent_logs.clear()

    def _analyze_connection_type(self, local_ip, remote_ip):
        """分析连接类型"""
        # 检查是否是TURN服务器地址
        turn_server_ip = "**************"

        logger.debug(f"🔍 分析连接类型: {local_ip} -> {remote_ip}")

        if local_ip == turn_server_ip or remote_ip == turn_server_ip:
            return "🔄 TURN中继连接"
        elif local_ip.startswith("192.168.") and remote_ip.startswith("192.168."):
            return "🏠 Host到Host直连（局域网）"
        elif local_ip.startswith("10.") and remote_ip.startswith("10."):
            return "🏠 Host到Host直连（移动网络）"
        elif (local_ip.startswith("192.168.") and not remote_ip.startswith("192.168.")) or \
             (not local_ip.startswith("192.168.") and remote_ip.startswith("192.168.")):
            # 一个是局域网地址，一个是公网地址 - 通常是STUN反射
            return "🌐 Host到STUN反射"
        elif local_ip.startswith("10.") or remote_ip.startswith("10."):
            # 可能是移动网络或其他私有网络
            if local_ip.startswith("192.168.") or remote_ip.startswith("192.168."):
                return "🌐 跨网络连接"
            else:
                return "🔄 可能的TURN中继"
        else:
            return "🌐 STUN反射连接"

# 创建全局ICE日志解析器
ice_log_parser = ICELogParser()

# 自定义日志处理器，用于捕获ICE日志
class ICELogHandler(logging.Handler):
    """捕获ICE相关日志的处理器"""

    def emit(self, record):
        try:
            # 只处理aioice相关的日志
            if record.name.startswith('aioice'):
                log_message = self.format(record)
                # 将日志添加到解析器
                ice_log_parser.add_log(log_message)
        except Exception:
            pass  # 忽略处理器内部错误

# 设置ICE日志捕获
def setup_ice_log_capture():
    """设置ICE日志捕获"""
    # 获取aioice的logger
    aioice_logger = logging.getLogger('aioice')

    # 创建并添加我们的处理器
    ice_handler = ICELogHandler()
    ice_handler.setLevel(logging.INFO)

    # 设置格式器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    ice_handler.setFormatter(formatter)

    # 添加处理器
    aioice_logger.addHandler(ice_handler)

    # 确保aioice日志级别足够低以捕获INFO级别的日志
    aioice_logger.setLevel(logging.INFO)

    logger.info("ICE日志捕获已设置")

# ICE候选项监控函数（用于调试和日志记录）
async def monitor_ice_candidates(pc, pc_id="PeerConnection"):
    """
    监控ICE候选项收集过程，记录不同类型的候选项和状态变更
    """
    # ICE候选项统计
    ice_stats = {
        'host': 0,
        'srflx': 0,
        'prflx': 0,
        'relay': 0,
        'total': 0
    }

    @pc.on("icecandidate")
    def on_icecandidate(candidate):
        if candidate:
            # 更新统计
            ice_stats['total'] += 1
            if candidate.type in ice_stats:
                ice_stats[candidate.type] += 1

            # 记录候选项类型用于调试
            if candidate.type == "relay":
                logger.info(f"{pc_id} 🔄 TURN中继候选项: {candidate.ip}:{candidate.port}")
            elif candidate.type == "srflx":
                logger.info(f"{pc_id} 🌐 STUN反射候选项: {candidate.ip}:{candidate.port}")
            elif candidate.type == "host":
                logger.info(f"{pc_id} 🏠 主机候选项: {candidate.ip}:{candidate.port}")
            elif candidate.type == "prflx":
                logger.info(f"{pc_id} 🔍 对等反射候选项: {candidate.ip}:{candidate.port}")
            else:
                logger.info(f"{pc_id} ❓ 其他候选项: {candidate.type} {candidate.ip}:{candidate.port}")

    @pc.on("icegatheringstatechange")
    def on_ice_gathering_state_change():
        logger.info(f"{pc_id} 📡 ICE收集状态变更: {pc.iceGatheringState}")
        if pc.iceGatheringState == "gathering":
            logger.info(f"{pc_id} 🔍 正在收集ICE候选项...")
        elif pc.iceGatheringState == "complete":
            logger.info(f"{pc_id} 📊 ICE主动收集完成！总计: {ice_stats}")
            logger.info(f"{pc_id} 📈 主动候选分布: Host={ice_stats['host']}, SRFLX={ice_stats['srflx']}, PRFLX={ice_stats['prflx']}, Relay={ice_stats['relay']}")
            if ice_stats['total'] == 0:
                logger.info(f"{pc_id} ⚠️ 注意：主动收集到0个候选项，可能依赖动态发现的peer reflexive候选项")

    @pc.on("iceconnectionstatechange")
    def on_ice_connection_state_change():
        logger.info(f"{pc_id} 🧊 ICE连接状态变更: {pc.iceConnectionState}")
        if pc.iceConnectionState == "checking":
            logger.info(f"{pc_id} 🔍 ICE正在检查可用连接...")
            # 异步显示候选项对信息
            asyncio.create_task(show_candidate_pairs_info(pc, pc_id))
        elif pc.iceConnectionState == "connected":
            logger.info(f"{pc_id} ✅ ICE连接已建立")
            # 异步显示最终选择的候选项对
            asyncio.create_task(show_selected_candidate_pair(pc, pc_id))
        elif pc.iceConnectionState == "completed":
            logger.info(f"{pc_id} ✅ ICE连接完成并已选择最佳路径")

            # 立即显示红色提示（不使用异步任务）
            logger.warning(f"{pc_id} 🔍 准备显示连接信息...")

            # 异步显示最终选择的候选项对
            asyncio.create_task(show_selected_candidate_pair(pc, pc_id))
            # 显示当前实际使用的连接信息（红色提示）
            try:
                asyncio.create_task(show_active_connection_info(pc, pc_id))
                logger.warning(f"{pc_id} ✅ 已创建连接信息显示任务")
            except Exception as e:
                logger.warning(f"{pc_id} ❌ 创建连接信息显示任务失败: {e}")
        elif pc.iceConnectionState == "failed":
            logger.info(f"{pc_id} ❌ ICE连接失败")
        elif pc.iceConnectionState == "disconnected":
            logger.info(f"{pc_id} ⚠️ ICE连接断开")
        elif pc.iceConnectionState == "closed":
            logger.info(f"{pc_id} 🔒 ICE连接已关闭")

    @pc.on("signalingstatechange")
    def on_signaling_state_change():
        logger.info(f"{pc_id} 📡 信令状态变更: {pc.signalingState}")
        if pc.signalingState == "have-remote-offer":
            logger.info(f"{pc_id} 📥 收到远程offer")
        elif pc.signalingState == "stable":
            logger.info(f"{pc_id} ✅ 信令状态稳定")
        elif pc.signalingState == "closed":
            logger.info(f"{pc_id} 🔒 信令连接关闭")

    @pc.on("connectionstatechange")
    def on_connection_state_change():
        logger.info(f"{pc_id} 🔗 连接状态变更: {pc.connectionState}")
        if pc.connectionState == "connecting":
            logger.info(f"{pc_id} 🔄 正在建立连接...")
        elif pc.connectionState == "connected":
            logger.info(f"{pc_id} ✅ 连接成功建立")
        elif pc.connectionState == "disconnected":
            logger.info(f"{pc_id} ⚠️ 连接断开")
        elif pc.connectionState == "failed":
            logger.info(f"{pc_id} ❌ 连接失败")
        elif pc.connectionState == "closed":
            logger.info(f"{pc_id} 🔒 连接已关闭")

    return pc

async def show_candidate_pairs_info(pc, pc_id):
    """
    显示ICE候选项对信息
    """
    try:
        # 等待一小段时间让ICE检查开始
        await asyncio.sleep(0.5)

        ice_transports = list(pc._RTCPeerConnection__iceTransports)
        if not ice_transports:
            logger.info(f"{pc_id} ⚠️ 没有找到ICE传输")
            return

        ice_transport = ice_transports[0]
        ice_connection = ice_transport._connection

        # 获取本地和远程候选项
        local_candidates = ice_connection.local_candidates
        remote_candidates = ice_connection.remote_candidates

        # 计算候选项对总数
        total_pairs = len(local_candidates) * len(remote_candidates)

        logger.info(f"{pc_id} ICE候选项: 本地{len(local_candidates)}个, 远程{len(remote_candidates)}个")

        if total_pairs > 0:
            logger.info(f"{pc_id} 🔍 ICE将测试所有候选项对组合，寻找最佳连接路径...")
        else:
            logger.warning(f"{pc_id} ⚠️ 没有候选项对可供测试！")

    except Exception as e:
        logger.error(f"{pc_id} ❌ 显示候选项对信息失败: {e}")

async def show_selected_candidate_pair(pc, pc_id):
    """
    显示最终选择的候选项对
    """
    try:
        ice_transports = list(pc._RTCPeerConnection__iceTransports)
        if not ice_transports:
            return

        ice_transport = ice_transports[0]
        ice_connection = ice_transport._connection

        # 获取候选项信息
        local_candidates = ice_connection.local_candidates
        remote_candidates = ice_connection.remote_candidates

        logger.info(f"{pc_id} 🎯 ICE连接建立成功！最终选择的连接路径:")

        # 尝试从aioice的内部状态获取选中的候选项对
        try:
            # 检查是否有选中的候选项对信息
            if hasattr(ice_connection, '_selected_pair') and ice_connection._selected_pair:
                selected_pair = ice_connection._selected_pair
                logger.info(f"{pc_id}   ✅ 选中的候选项对:")
                logger.info(f"{pc_id}     本地: {selected_pair.local_candidate}")
                logger.info(f"{pc_id}     远程: {selected_pair.remote_candidate}")
                logger.info(f"{pc_id}     状态: {selected_pair.state}")
            else:
                # 如果无法直接获取，显示可能的连接信息
                logger.info(f"{pc_id}   📊 连接统计信息:")
                logger.info(f"{pc_id}     本地候选项总数: {len(local_candidates)}")
                logger.info(f"{pc_id}     远程候选项总数: {len(remote_candidates)}")

                # 显示最高优先级的候选项（通常是被选中的）
                if local_candidates and remote_candidates:
                    logger.info(f"{pc_id}   🔍 推测使用的候选项（基于优先级）:")

                    # 本地候选项通常按优先级排序
                    best_local = local_candidates[0]
                    local_str = str(best_local)
                    local_parts = local_str.split()
                    if len(local_parts) >= 7:
                        logger.info(f"{pc_id}     可能的本地候选项: {local_parts[6]} {local_parts[4]}:{local_parts[5]}")

                    # 远程候选项
                    best_remote = remote_candidates[0]
                    remote_str = str(best_remote)
                    remote_parts = remote_str.split()
                    if len(remote_parts) >= 7:
                        logger.info(f"{pc_id}     可能的远程候选项: {remote_parts[6]} {remote_parts[4]}:{remote_parts[5]}")

                    # 分析连接类型
                    if len(local_parts) >= 7 and len(remote_parts) >= 7:
                        local_type = local_parts[6]
                        remote_type = remote_parts[6]
                        local_addr = local_parts[4]
                        remote_addr = remote_parts[4]

                        logger.info(f"{pc_id}   🔗 连接类型分析:")
                        if local_type == "host" and remote_type == "host":
                            if local_addr.startswith("192.168.") and remote_addr.startswith("192.168."):
                                logger.info(f"{pc_id}     📍 局域网直连 (Host ↔ Host)")
                            else:
                                logger.info(f"{pc_id}     🌐 公网直连 (Host ↔ Host)")
                        elif local_type == "host" and remote_type == "prflx":
                            logger.info(f"{pc_id}     🔍 混合连接 (Host ↔ Peer Reflexive)")
                        elif local_type == "srflx" or remote_type == "srflx":
                            logger.info(f"{pc_id}     🌐 STUN反射连接")
                        elif local_type == "relay" or remote_type == "relay":
                            logger.info(f"{pc_id}     🔄 TURN中继连接")
                        else:
                            logger.info(f"{pc_id}     ❓ 其他类型连接: {local_type} ↔ {remote_type}")

        except Exception as e:
            logger.debug(f"{pc_id} 获取详细候选项对信息失败: {e}")

        # 显示ICE角色信息
        try:
            ice_controlling = getattr(ice_connection, 'ice_controlling', None)
            if ice_controlling is not None:
                role = "Controlling (控制方)" if ice_controlling else "Controlled (被控制方)"
                logger.info(f"{pc_id}   🎭 ICE角色: {role}")
        except:
            pass

    except Exception as e:
        logger.error(f"{pc_id} ❌ 显示选中候选项对失败: {e}")

def analyze_client_sdp(sdp_content, client_host):
    """简单分析客户端SDP中的ICE候选项"""
    try:
        sdp_lines = sdp_content.split('\n')
        candidate_lines = [line for line in sdp_lines if line.startswith('a=candidate:')]

        # 统计候选项类型
        candidate_types = {'host': 0, 'srflx': 0, 'relay': 0, 'prflx': 0}
        for line in candidate_lines:
            parts = line.split()
            if len(parts) >= 8:
                candidate_type = parts[7]
                if candidate_type in candidate_types:
                    candidate_types[candidate_type] += 1

        logger.info(f"客户端SDP分析 ({client_host}): {len(candidate_lines)}个候选项 "
                   f"(host:{candidate_types['host']}, srflx:{candidate_types['srflx']}, "
                   f"relay:{candidate_types['relay']}, prflx:{candidate_types['prflx']})")

        return len(candidate_lines)
    except Exception as e:
        logger.error(f"分析客户端SDP失败: {e}")
        return 0

def force_h264_only_sdp(sdp_text):
    """完全移除VP8支持，只保留H264"""
    logger.info("🔧 force_h264_only_sdp 函数被调用")
    logger.info(f"📝 原始SDP长度: {len(sdp_text)} 字符")

    lines = sdp_text.split('\n')
    filtered_lines = []

    for line in lines:
        # 跳过VP8/VP9/AV1相关的rtpmap行
        if any(x in line for x in ['VP8/90000', 'VP9/90000', 'AV1/90000']):
            continue
        # 跳过VP8/VP9/AV1相关的fmtp行
        if any(x in line for x in ['fmtp:97 apt=96', 'fmtp:99 apt=98', 'fmtp:36 apt=35']):
            continue

        # 保持客户端原始的H264参数，不做修改
        if 'fmtp:100' in line:
            # 保持客户端发送的原始H264参数，避免编解码器不匹配
            logger.info(f"🔧 保持客户端原始H264参数: {line}")
            filtered_lines.append(line)
            continue

        # 修改m=video行，只保留H264相关payload
        if line.startswith('m=video'):
            parts = line.split(' ')
            # 只保留H264 (100)、RTX (101)、RED (125)、RTX for RED (124)、ULPFEC (127)
            h264_payloads = [p for p in parts[3:] if p in ['100', '101', '125', '124', '127']]
            line = f"{parts[0]} {parts[1]} {parts[2]} {' '.join(h264_payloads)}"
            logger.info(f"🔧 强制H264: 修改视频媒体行为 H264 only")

        filtered_lines.append(line)

    logger.info("✅ SDP已强制为H264编解码器")

    # 输出修改后的SDP用于调试
    modified_sdp = '\n'.join(filtered_lines)
    logger.info(f"📝 修改后SDP长度: {len(modified_sdp)} 字符")

    # 查找修改后的视频媒体行
    for line in filtered_lines:
        if line.startswith('m=video'):
            logger.info(f"🎬 修改后视频媒体行: {line}")
            break

    return modified_sdp

# 低延迟优化配置
def optimize_low_latency():
    """优化WebRTC低延迟设置"""
    # 获取低延迟配置
    config = get_low_latency_config()

    if not config.enabled:
        logger.info("📺 低延迟模式未启用，使用标准配置")
        return

    # 记录配置信息
    config.log_configuration()

    # 修改aiortc的默认JitterBuffer设置
    import aiortc.rtcrtpreceiver

    # 保存原始的RTCRtpReceiver初始化方法
    original_init = aiortc.rtcrtpreceiver.RTCRtpReceiver.__init__

    # 获取缓冲区配置
    buffer_config = config.get_server_jitter_buffer_config()

    def optimized_init(self, kind, transport):
        # 调用原始初始化
        original_init(self, kind, transport)

        # 根据配置优化缓冲区设置
        if kind == "video":
            from aiortc.jitterbuffer import JitterBuffer
            video_config = buffer_config['video']
            self._RTCRtpReceiver__jitter_buffer = JitterBuffer(
                capacity=video_config['capacity'],
                prefetch=video_config['prefetch'],
                is_video=video_config['is_video']
            )
    
        elif kind == "audio":
            from aiortc.jitterbuffer import JitterBuffer
            audio_config = buffer_config['audio']
            self._RTCRtpReceiver__jitter_buffer = JitterBuffer(
                capacity=audio_config['capacity'],
                prefetch=audio_config['prefetch']
            )


    # 应用优化
    aiortc.rtcrtpreceiver.RTCRtpReceiver.__init__ = optimized_init


    # H264编解码器使用默认配置
    logger.info("✅ 使用aiortc默认H264编解码器配置")

# 设置ICE服务器 - 从环境变量读取配置
def create_ice_configuration(force_relay=False):
    """Create ICE configuration from environment variables"""

    config_manager = get_stun_turn_config()

    # 检查是否有配置
    if not config_manager.is_configured():
        logger.warning("未配置STUN/TURN服务器，使用空ICE配置")
        return RTCConfiguration(bundlePolicy="max-bundle")

    # 获取完整的服务端ICE服务器配置
    ice_servers = config_manager.get_server_ice_servers()

    # 创建RTCConfiguration
    config = RTCConfiguration(
        iceServers=ice_servers,
        bundlePolicy="max-bundle"
    )

    # 检查是否强制使用TURN中继
    if force_relay and any(server for server in config_manager.turn_servers):
        # 强制使用relay模式，禁用host和srflx候选项
        config.iceTransportPolicy = "relay"
        logger.info(f"🔒 强制使用TURN中继模式，服务器数量: {len(ice_servers)}")
        logger.info("⚠️ 已禁用host和srflx候选项，仅使用TURN中继")
    else:
        # 不强制使用relay模式，让ICE自动选择最佳路径
        logger.info(f"🌐 使用完整ICE配置，服务器数量: {len(ice_servers)}")
        if any(server for server in config_manager.turn_servers):
            logger.info("TURN服务器可用，ICE将根据网络条件自动选择连接方式")

    return config

# ICE配置现在根据客户端类型动态创建

async def show_active_connection_info(pc, pc_id):
    """
    显示当前实际使用的连接信息（红色提示）
    """
    global connection_info_shown

    # 防止重复显示 - 使用PC对象作为唯一标识
    pc_object_id = id(pc)  # 使用PC对象的内存地址作为唯一标识

    if pc_object_id in connection_info_shown:
        logger.debug(f"{pc_id} 连接信息已显示过，跳过重复显示")
        return
    connection_info_shown.add(pc_object_id)

    try:
        await asyncio.sleep(2)  # 等待连接稳定

        # 直接从PC对象的ICE连接中获取实际使用的候选项对
        active_local_candidate = None
        active_remote_candidate = None
        connection_type = "未知"

        # 使用ICE日志解析器获取连接信息
        logger.warning(f"{pc_id} 🔍 开始获取ICE连接信息...")

        connection_info = ice_log_parser.parse_successful_connection()
        if connection_info:
            logger.warning(f"{pc_id} 从ICE日志解析到连接信息: {connection_info['connection_type']}")

            # 显示红色提示信息（一行简洁显示）
            local_info = f"{connection_info['local_ip']}:{connection_info['local_port']}"
            remote_info = f"{connection_info['remote_ip']}:{connection_info['remote_port']}"
            logger.warning(f"🔴 {pc_id} {connection_info['connection_type']} | 本地: {local_info} | 远程: {remote_info}")
        else:
            logger.warning(f"{pc_id} ICE日志解析未找到连接信息，显示为未知")
            logger.warning(f"🔴 {pc_id} ❓ 连接类型未知 | 本地: unknown:unknown | 远程: unknown:unknown")

    except Exception as e:
        logger.debug(f"{pc_id} 获取活跃连接信息失败: {e}")
        # 即使失败也显示未知信息
        logger.warning(f"🔴 {pc_id} ❓ 连接类型未知 | 本地: unknown:unknown | 远程: unknown:unknown")

# 修改的ICE候选者过滤函数，不再使用_gather_candidates
async def filter_candidates(pc):
    """
    使用标准RTCPeerConnection接口配置ICE候选者
    """
    # 这里我们可以在创建PC之后，通过事件监听过滤候选者
    @pc.on("icecandidate")
    def on_icecandidate(candidate):
        # 记录所有候选者信息用于调试
        if candidate and candidate.ip:
            # 只过滤掉链路本地地址（169.254.x.x），保留局域网地址
            if not candidate.ip.startswith("169.254"):
                logger.info(f"ICE候选者: {candidate.type} {candidate.ip}:{candidate.port}")
    
    return pc


class VideoTransformTrack(MediaStreamTrack):
    """
    A video stream track that transforms frames from another track.
    Based on aiortc example/server implementation.
    """
    kind = "video"

    def __init__(self, track, transform):
        super().__init__()  # don't forget this!
        self.track = track
        self.transform = transform
        self.decode_error_count = 0
        self.total_frames = 0
        self.last_error_log_time = 0

    async def recv(self):
        frame = await self.track.recv()

        if self.transform == "cartoon":
            img = frame.to_ndarray(format="bgr24")

            # prepare color
            img_color = cv2.pyrDown(cv2.pyrDown(img))
            for _ in range(6):
                img_color = cv2.bilateralFilter(img_color, 9, 9, 7)
            img_color = cv2.pyrUp(cv2.pyrUp(img_color))

            # prepare edges
            img_edges = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            img_edges = cv2.adaptiveThreshold(
                cv2.medianBlur(img_edges, 7),
                255,
                cv2.ADAPTIVE_THRESH_MEAN_C,
                cv2.THRESH_BINARY,
                9,
                2,
            )
            img_edges = cv2.cvtColor(img_edges, cv2.COLOR_GRAY2RGB)

            # combine color and edges
            img = cv2.bitwise_and(img_color, img_edges)

            # rebuild a VideoFrame, preserving timing information
            new_frame = VideoFrame.from_ndarray(img, format="bgr24")
            new_frame.pts = frame.pts
            new_frame.time_base = frame.time_base
            return new_frame
        elif self.transform == "edges":
            # perform edge detection
            img = frame.to_ndarray(format="bgr24")
            img = cv2.cvtColor(cv2.Canny(img, 100, 200), cv2.COLOR_GRAY2BGR)

            # rebuild a VideoFrame, preserving timing information
            new_frame = VideoFrame.from_ndarray(img, format="bgr24")
            new_frame.pts = frame.pts
            new_frame.time_base = frame.time_base
            return new_frame
        elif self.transform == "rotate":
            # rotate image
            img = frame.to_ndarray(format="bgr24")
            rows, cols, _ = img.shape
            M = cv2.getRotationMatrix2D((cols / 2, rows / 2), frame.time * 45, 1)
            img = cv2.warpAffine(img, M, (cols, rows))

            # rebuild a VideoFrame, preserving timing information
            new_frame = VideoFrame.from_ndarray(img, format="bgr24")
            new_frame.pts = frame.pts
            new_frame.time_base = frame.time_base
            return new_frame
        elif self.transform == "pose":
            # YOLO pose detection - integrated directly
            img = frame.to_ndarray(format="bgr24")
            
            try:
                model = load_yolo_model()
                if model is not None:
                    img = self._apply_yolo_pose_detection(img, model)
                else:
                    # Fallback when model not available
                    cv2.putText(img, "YOLO MODEL NOT AVAILABLE", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            except Exception as e:
                logger.error(f"YOLO processing failed: {e}")
                cv2.putText(img, "YOLO PROCESSING FAILED", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

            # rebuild a VideoFrame, preserving timing information
            new_frame = VideoFrame.from_ndarray(img, format="bgr24")
            new_frame.pts = frame.pts
            new_frame.time_base = frame.time_base
            return new_frame
        else:
            return frame

    def _apply_yolo_pose_detection(self, img, model):
        """Apply YOLO pose detection to image"""
        try:
            # Get original dimensions
            original_height, original_width = img.shape[:2]
            
            # Preprocess for YOLO (resize to 640x640)
            input_size = 640
            resized = cv2.resize(img, (input_size, input_size))
            normalized = resized.astype(np.float32) / 255.0
            input_tensor = normalized.transpose(2, 0, 1)[np.newaxis, ...]  # NCHW format
            
            # Run inference
            input_name = model.get_inputs()[0].name
            predictions = model.run(None, {input_name: input_tensor})[0]
            
            # Process predictions (simplified)
            if len(predictions.shape) == 3:
                predictions = predictions[0]  # Remove batch dimension
            if predictions.shape[0] == 56:  # YOLO pose format
                predictions = predictions.T  # Transpose to (N, 56)
            
            # Draw detections
            for pred in predictions:
                if len(pred) >= 5:
                    confidence = pred[4]
                    if confidence > 0.3:  # Confidence threshold
                        # Scale bounding box to original image
                        x_center = pred[0] * original_width
                        y_center = pred[1] * original_height
                        width = pred[2] * original_width
                        height = pred[3] * original_height
                        
                        x1 = int(x_center - width / 2)
                        y1 = int(y_center - height / 2)
                        x2 = int(x_center + width / 2)
                        y2 = int(y_center + height / 2)
                        
                        # Ensure coordinates are within image bounds
                        x1 = max(0, min(x1, original_width - 1))
                        y1 = max(0, min(y1, original_height - 1))
                        x2 = max(0, min(x2, original_width - 1))
                        y2 = max(0, min(y2, original_height - 1))
                        
                        # Draw bounding box
                        cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(img, f"{confidence:.2f}", (x1, y1-10), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                        
                        # Draw keypoints if available
                        if len(pred) >= 56:  # Has keypoints
                            keypoints = pred[5:56].reshape(17, 3)  # 17 keypoints, 3 values each
                            for kp in keypoints:
                                if kp[2] > 0.3:  # Keypoint confidence
                                    x = int(kp[0] * original_width)
                                    y = int(kp[1] * original_height)
                                    if 0 <= x < original_width and 0 <= y < original_height:
                                        cv2.circle(img, (x, y), 4, (255, 0, 0), -1)
            
            # Add processing indicator
            cv2.putText(img, "YOLO POSE DETECTION", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
        except Exception as e:
            logger.error(f"YOLO processing error: {e}")
            cv2.putText(img, "YOLO ERROR", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        
        return img


async def handle_whip_publish(request):
    """
    处理WHIP推流请求
    """
    stream_id = str(uuid.uuid4())
    content = await request.read()

    # 清理ICE日志缓存，确保每次新连接都能获取最新的连接信息
    ice_log_parser.clear_logs()

    logger.info(f"收到WHIP推流请求，分配流ID: {stream_id}")

    # 记录客户端信息
    client_host = request.remote
    logger.info(f"客户端地址: {client_host}")

    # 分析客户端发送的SDP
    offer_sdp = content.decode('utf-8')
    analyze_client_sdp(offer_sdp, client_host)

    # 创建完整的ICE配置
    rtc_configuration = create_ice_configuration()
    
    pc = RTCPeerConnection(configuration=rtc_configuration)

    pc_id = f"PeerConnection({stream_id})"

    # 监控ICE候选项（用于调试）
    pc = await monitor_ice_candidates(pc, pc_id)
    peer_connections[stream_id] = pc
    
    def log_info(msg, *args):
        logger.info(f"{pc_id} {msg}", *args)
    
    # 注意：ICE连接状态变化已经在monitor_ice_candidates中处理，这里不需要重复监听

    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        if pc.connectionState == "failed":
            logger.error(f"{pc_id} 连接失败")
            await cleanup_peer_connection(stream_id)
        elif pc.connectionState == "closed":
            await cleanup_peer_connection(stream_id)
    
    # 监控数据通道
    @pc.on("datachannel")
    def on_datachannel(channel):
        log_info("收到新的数据通道: %s", channel.label)
        
        @channel.on("message")
        def on_message(message):
            log_info("数据通道消息: %s", message)
    
    # 处理媒体轨道
    @pc.on("track")
    def on_track(track):
        logger.info(f"收到轨道: {track.kind}")
        if track.kind == "video":
            # 存储原始轨道用于后续拉流
            streams[stream_id] = track
            logger.info(f"存储原始视频轨道，流ID: {stream_id}")
        elif track.kind == "audio":
            # 音频轨道也存储，但目前主要处理视频
            logger.info(f"收到音频轨道，流ID: {stream_id}")

        @track.on("ended")
        async def on_ended():
            logger.info(f"轨道结束: {track.kind}")
            # 不立即删除流，等待连接完全断开时再清理
            logger.info(f"轨道 {track.kind} 已结束，但保留流 {stream_id} 等待连接清理")
    
    # 处理offer - 强制H264编解码器
    try:
        offer_sdp = content.decode()

        # 强制移除VP8支持，只保留H264
        modified_offer_sdp = force_h264_only_sdp(offer_sdp)

        offer = RTCSessionDescription(sdp=modified_offer_sdp, type="offer")
        await pc.setRemoteDescription(offer)
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)
        
        # 返回answer
        return web.Response(
            content_type="application/sdp",
            text=pc.localDescription.sdp
        )
    except Exception as e:
        logger.error(f"{pc_id} 处理offer时发生错误: {str(e)}")
        await cleanup_peer_connection(stream_id)
        raise


async def handle_whep_consume(request):
    """
    处理WHEP拉流请求
    """
    stream_id = request.match_info["stream_id"]
    content = await request.read()
    
    logger.info(f"收到WHEP拉流请求，请求流ID: {stream_id}")
    
    # 检查流是否存在
    if stream_id not in streams:
        return web.Response(status=404, text="Stream not found")
    
    # 记录客户端信息
    client_host = request.remote
    logger.info(f"WHEP客户端地址: {client_host}")

    # 获取查询参数，检查是否需要应用AI处理
    query = request.query
    apply_ai = query.get("ai", "false").lower() == "true"
    transform_type = query.get("transform", None)  # 新增：获取transform参数
    force_relay = query.get("force_relay", "false").lower() == "true"  # 强制使用TURN中继

    # 创建ICE配置（支持强制TURN中继）
    rtc_config = create_ice_configuration(force_relay=force_relay)
    pc = RTCPeerConnection(configuration=rtc_config)

    # 生成消费者ID
    consumer_id = str(uuid.uuid4())

    # 监控ICE候选项和应用候选者过滤
    pc = await monitor_ice_candidates(pc, f"WHEP-Consumer({consumer_id})")
    pc = await filter_candidates(pc)

    peer_connections[consumer_id] = pc

    # 添加轨道到对等连接 - 使用MediaRelay正确处理
    original_track = streams[stream_id]
    track = relay.subscribe(original_track)  # 使用MediaRelay创建新的轨道实例

    # 定义有效的transform类型
    valid_transforms = ["pose", "edges", "cartoon", "rotate", "none"]

    # 决定是否应用transform
    if transform_type:
        # 如果指定了transform参数，验证其有效性
        if transform_type not in valid_transforms:
            logger.warning(f"无效的transform类型: {transform_type}，使用默认值pose")
            transform_type = "pose"
        # 添加带指定transform的轨道
        pc.addTrack(VideoTransformTrack(track, transform=transform_type))
        logger.info(f"消费者 {consumer_id} 应用transform: {transform_type}")
    elif apply_ai:
        # 向后兼容：如果使用ai=true，默认使用pose
        pc.addTrack(VideoTransformTrack(track, transform="pose"))
        logger.info(f"消费者 {consumer_id} 应用AI处理: pose (默认)")
    else:
        # 添加原始轨道，无处理
        pc.addTrack(track)
        logger.info(f"消费者 {consumer_id} 添加原始轨道")

    # 设置SDP - 在添加轨道之后
    offer = RTCSessionDescription(sdp=content.decode(), type="offer")
    await pc.setRemoteDescription(offer)

    # 创建应答 - 添加错误处理
    try:
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)
        logger.info(f"消费者 {consumer_id} SDP协商完成")
    except Exception as e:
        logger.error(f"消费者 {consumer_id} SDP协商失败: {str(e)}")
        await cleanup_peer_connection(consumer_id)
        raise web.HTTPInternalServerError(text=f"SDP negotiation failed: {str(e)}")
    
    # 等待ICE收集完成（关键修复）
    if pc.iceGatheringState != "complete":
        logger.info(f"消费者 {consumer_id} 等待ICE候选者收集完成...")
        ice_complete = asyncio.Event()
        
        def on_ice_gathering_state_change():
            if pc.iceGatheringState == "complete":
                ice_complete.set()
        
        pc.on("icegatheringstatechange", on_ice_gathering_state_change)
        
        # 等待ICE收集完成，最多等待30秒
        try:
            await asyncio.wait_for(ice_complete.wait(), timeout=30.0)
            logger.info(f"消费者 {consumer_id} ICE候选者收集完成")
        except asyncio.TimeoutError:
            logger.info(f"消费者 {consumer_id} ICE候选者收集超时，继续处理")
    
    # 设置连接关闭处理
    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        logger.info(f"消费者连接状态变更为 {pc.connectionState}")
        if pc.connectionState == "failed" or pc.connectionState == "closed":
            await cleanup_peer_connection(consumer_id)
    
    # 返回应答
    return web.Response(
        content_type="application/sdp",
        text=pc.localDescription.sdp,
        headers={
            "Location": f"/whep/{stream_id}/{consumer_id}",
            "Link": '<https://www.ietf.org/archive/id/draft-ietf-wish-whep-00.txt>; rel="describedby"'
        }
    )


async def handle_ai_config(request):
    """
    处理AI配置请求 - 简化版本（AI现在集成在VideoTransformTrack中）
    """
    return web.Response(
        status=200,
        content_type="application/json",
        text='{"message": "AI configuration is now integrated directly into VideoTransformTrack. Use video_transform parameter in offer requests."}'
    )


async def handle_ice_config(request):
    """
    处理ICE配置请求，返回客户端可用的STUN/TURN服务器配置
    """
    # 记录客户端信息
    client_host = request.remote

    config_manager = get_stun_turn_config()
    ice_servers = config_manager.get_client_ice_servers()

    response_data = {
        "iceServers": ice_servers,
        "configured": config_manager.is_configured(),
        "summary": config_manager.get_config_summary()
    }

    logger.info(f"ICE配置请求 - 客户端: {client_host}, 服务器数量: {len(ice_servers)}")

    return web.Response(
        content_type="application/json",
        text=json.dumps(response_data)
    )


async def handle_low_latency_config(request):
    """
    处理低延迟配置请求，返回客户端优化参数
    """
    # 记录客户端信息
    client_host = request.remote

    # 获取低延迟配置
    from low_latency_config import get_client_optimization_config
    config_data = get_client_optimization_config()

    logger.info(f"低延迟配置请求 - 客户端: {client_host}")

    return web.Response(
        content_type="application/json",
        text=json.dumps(config_data)
    )


async def handle_options(request):
    """
    处理OPTIONS请求
    """
    return web.Response(
        headers={
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
        }
    )


async def handle_list_streams(request):
    """
    列出所有活跃的流
    """
    active_streams = []
    for stream_id, track in streams.items():
        active_streams.append({
            "id": stream_id,
            "whep_url": f"http://{request.host}/whep/{stream_id}",
            "whep_url_with_ai": f"http://{request.host}/whep/{stream_id}?ai=true",
            "transform_urls": {
                "pose": f"http://{request.host}/whep/{stream_id}?transform=pose",
                "edges": f"http://{request.host}/whep/{stream_id}?transform=edges",
                "cartoon": f"http://{request.host}/whep/{stream_id}?transform=cartoon",
                "rotate": f"http://{request.host}/whep/{stream_id}?transform=rotate",
                "none": f"http://{request.host}/whep/{stream_id}?transform=none"
            },
            "force_relay_urls": {
                "pose": f"http://{request.host}/whep/{stream_id}?transform=pose&force_relay=true",
                "edges": f"http://{request.host}/whep/{stream_id}?transform=edges&force_relay=true",
                "cartoon": f"http://{request.host}/whep/{stream_id}?transform=cartoon&force_relay=true",
                "rotate": f"http://{request.host}/whep/{stream_id}?transform=rotate&force_relay=true",
                "none": f"http://{request.host}/whep/{stream_id}?transform=none&force_relay=true",
                "basic": f"http://{request.host}/whep/{stream_id}?force_relay=true"
            }
        })
    
    return web.Response(
        content_type="application/json",
        text=json.dumps({
            "streams": active_streams,
            "count": len(active_streams),
            "available_transforms": ["pose", "edges", "cartoon", "rotate", "none"],
            "force_relay_available": True,
            "force_relay_info": "添加 ?force_relay=true 参数强制使用TURN中继"
        }, indent=2)
    )


async def handle_offer(request):
    """
    Handle offer/answer requests - simplified based on aiortc example
    """
    params = await request.json()
    offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])

    # 记录客户端信息
    client_host = request.remote
    logger.info(f"Offer客户端地址: {client_host}")

    # 创建完整的ICE配置
    rtc_config = create_ice_configuration()
    pc = RTCPeerConnection(configuration=rtc_config)
    pc_id = "PeerConnection(%s)" % uuid.uuid4()
    pcs.add(pc)

    def log_info(msg, *args):
        logger.info(pc_id + " " + msg, *args)

    log_info("Created for %s", request.remote)

    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        log_info("Connection state is %s", pc.connectionState)
        if pc.connectionState == "failed":
            await pc.close()
            pcs.discard(pc)

    @pc.on("track")
    def on_track(track):
        log_info("Track %s received", track.kind)

        if track.kind == "video":
            # Create transform track (YOLO is now integrated directly)
            transform_track = VideoTransformTrack(
                relay.subscribe(track), 
                transform=params.get("video_transform", "none")
            )
            log_info("Transform track created with: %s", params.get("video_transform", "none"))
            
            pc.addTrack(transform_track)

        @track.on("ended")
        async def on_ended():
            log_info("Track %s ended", track.kind)

    # handle offer
    await pc.setRemoteDescription(offer)
    
    # send answer
    answer = await pc.createAnswer()
    await pc.setLocalDescription(answer)

    return web.Response(
        content_type="application/json",
        text=json.dumps(
            {"sdp": pc.localDescription.sdp, "type": pc.localDescription.type}
        ),
    )


async def cleanup_peer_connection(pc_id):
    """
    清理对等连接
    """
    global connection_info_shown

    try:
        logger.info(f"开始清理连接 {pc_id}")

        # 清理连接信息显示标记
        if pc_id in peer_connections:
            pc = peer_connections[pc_id]
            pc_object_id = id(pc)
            connection_info_shown.discard(pc_object_id)

        if pc_id in peer_connections:
            pc = peer_connections.pop(pc_id)

            # 清理ICE候选项映射
            if pc in ice_sdp_fixer.pc_candidates_map:
                del ice_sdp_fixer.pc_candidates_map[pc]
                logger.debug(f"已清理连接 {pc_id} 的ICE候选项映射")

            await pc.close()
            logger.info(f"已关闭对等连接 {pc_id}")

        # 清理AI处理器
        # AI processors are now integrated into VideoTransformTrack, no manual cleanup needed

        # 检查是否是流发布者断开
        if pc_id in streams:
            # 检查是否还有其他活跃的消费者
            active_consumers = [cid for cid in peer_connections.keys() if cid != pc_id]
            if not active_consumers:
                del streams[pc_id]
                logger.info(f"流 {pc_id} 已被移除（无活跃消费者）")
            else:
                logger.info(f"流 {pc_id} 保留（还有 {len(active_consumers)} 个活跃消费者）")
    except Exception as e:
        logger.error(f"清理连接时出错 {pc_id}: {str(e)}")
    finally:
        # 确保从所有集合中移除
        peer_connections.pop(pc_id, None)
        streams.pop(pc_id, None)


async def cleanup_all_resources():
    """
    清理所有资源的通用函数
    """
    logger.info("🧹 开始清理所有资源...")
    
    try:
        # 1. 关闭所有对等连接 (example style)
        if pcs:
            logger.info(f"关闭 {len(pcs)} 个示例风格的对等连接...")
            coros = [pc.close() for pc in pcs if pc]
            await asyncio.gather(*coros, return_exceptions=True)
            pcs.clear()
            logger.info("✅ 示例风格连接已关闭")
        
        # 2. 关闭WHIP/WHEP连接
        if peer_connections:
            logger.info(f"关闭 {len(peer_connections)} 个WHIP/WHEP连接...")
            for pc_id, pc in list(peer_connections.items()):
                try:
                    if pc:
                        await pc.close()
                        logger.debug(f"关闭连接: {pc_id}")
                except Exception as e:
                    logger.warning(f"关闭连接 {pc_id} 时出错: {e}")
            peer_connections.clear()
            logger.info("✅ WHIP/WHEP连接已关闭")
        
        # 3. 清理媒体流
        if streams:
            logger.info(f"清理 {len(streams)} 个媒体流...")
            streams.clear()
            logger.info("✅ 媒体流已清理")
        
        # 4. 停止媒体中继
        global relay
        if relay:
            try:
                # MediaRelay没有显式的close方法，但我们可以让它被垃圾回收
                relay = None
                logger.info("✅ 媒体中继已停止")
            except Exception as e:
                logger.warning(f"停止媒体中继时出错: {e}")
        
        # 5. 清理YOLO模型
        global _yolo_model
        if _yolo_model:
            try:
                _yolo_model = None
                logger.info("✅ YOLO模型已清理")
            except Exception as e:
                logger.warning(f"清理YOLO模型时出错: {e}")
        
        logger.info("🎉 所有资源清理完毕")
        
    except Exception as e:
        logger.error(f"❌ 资源清理过程中出错: {e}")


async def on_shutdown(app):
    """
    应用关闭时的清理工作
    """
    logger.info("📡 服务器正在关闭...")
    await cleanup_all_resources()


def create_ssl_context(cert_file, key_file):
    """
    创建SSL上下文
    """
    ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
    ssl_context.load_cert_chain(cert_file, key_file)
    return ssl_context


# 全局变量存储应用实例，用于信号处理
app_instance = None
server_runner = None


async def graceful_shutdown(signum=None):
    """
    优雅关闭服务器
    """
    if signum:
        logger.info(f"🚨 收到信号 {signum}，开始优雅关闭...")
    else:
        logger.info("🚨 开始优雅关闭服务器...")
    
    try:
        # 清理所有WebRTC资源
        await cleanup_all_resources()
        
        # 关闭HTTP服务器
        if server_runner:
            logger.info("🌐 关闭HTTP服务器...")
            await server_runner.cleanup()
            logger.info("✅ HTTP服务器已关闭")
        
        logger.info("🎯 服务器已完全关闭")
        
    except Exception as e:
        logger.error(f"❌ 优雅关闭过程中出错: {e}")
    finally:
        # 确保程序退出
        sys.exit(0)


def signal_handler(signum, frame):
    """
    信号处理器 - 同步版本，用于启动异步清理
    """
    logger.info(f"🔔 接收到信号 {signum}")
    
    # 获取当前事件循环
    try:
        loop = asyncio.get_running_loop()
        # 创建任务进行优雅关闭
        loop.create_task(graceful_shutdown(signum))
    except RuntimeError:
        # 如果没有运行的事件循环，创建一个新的
        asyncio.run(graceful_shutdown(signum))


async def run_server(args):
    """
    运行服务器的异步函数
    """
    global app_instance, server_runner
    
    try:
        logger.info("🚀 正在启动WHIP/WHEP WebRTC服务器...")
        
        # 创建web应用
        app = web.Application()
        app_instance = app
        
        # 设置CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods=["GET", "POST", "OPTIONS"]
            )
        })
        
        # 设置路由
        app.router.add_post("/whip", handle_whip_publish)
        app.router.add_post("/whep/{stream_id}", handle_whep_consume)
        app.router.add_post("/ai/{consumer_id}/config", handle_ai_config)
        app.router.add_get("/ice-config", handle_ice_config)  # ICE配置端点
        app.router.add_get("/low-latency-config", handle_low_latency_config)  # 低延迟配置端点
        app.router.add_post("/offer", handle_offer)  # 兼容官方示例的端点
        app.router.add_get("/streams", handle_list_streams)  # 列出所有流
        
        # 添加favicon处理
        async def serve_favicon(request):
            return web.Response(status=204)  # No Content
        
        # 添加主demo页面处理器
        async def serve_index_html(request):
            try:
                with open('index.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                return web.Response(text=content, content_type='text/html')
            except FileNotFoundError:
                return web.Response(status=404, text="index.html not found")
        
        async def serve_client_js(request):
            try:
                with open('client.js', 'r', encoding='utf-8') as f:
                    content = f.read()
                return web.Response(text=content, content_type='application/javascript')
            except FileNotFoundError:
                return web.Response(status=404, text="client.js not found")

        async def serve_test_whep(request):
            try:
                with open('test_whep.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                return web.Response(text=content, content_type='text/html')
            except FileNotFoundError:
                return web.Response(status=404, text="test_whep.html not found")



        # 设置主demo页面为主页
        app.router.add_get("/", serve_index_html)  # 主页直接使用index.html
        app.router.add_get("/index.html", serve_index_html)
        app.router.add_get("/client.js", serve_client_js)
        app.router.add_get("/test_whep.html", serve_test_whep)  # 拉流测试页面
        app.router.add_get("/favicon.ico", serve_favicon)
        
        # 添加API信息页面（仅用于API端点说明）
        async def serve_api_info(request):
            return web.Response(text="""<!DOCTYPE html>
<html><head><title>WebRTC API Information</title><meta charset="utf-8">
<style>body{font-family:Arial,sans-serif;margin:40px;background:#f5f5f5;}
.container{background:white;padding:30px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);max-width:800px;}
h1{color:#333;}.endpoint{background:#f8f9fa;padding:15px;border-radius:5px;margin:10px 0;border-left:4px solid #007bff;}
code{background:#e9ecef;padding:2px 6px;border-radius:3px;font-family:monospace;}.method{color:#28a745;font-weight:bold;}
.back-link{display:inline-block;padding:10px 20px;background:#007bff;color:white;text-decoration:none;border-radius:5px;margin:20px 0;}
.back-link:hover{background:#0056b3;}.example{background:#f0f0f0;padding:10px;margin:5px 0;border-radius:3px;font-family:monospace;font-size:14px;}
.param-table{width:100%;border-collapse:collapse;margin:10px 0;}.param-table th,.param-table td{border:1px solid #ddd;padding:8px;text-align:left;}
.param-table th{background:#f8f9fa;}</style></head><body><div class="container">
<h1>📡 WebRTC API 端点</h1><a href="/" class="back-link">← 返回主页</a>
<div class="endpoint"><h3><span class="method">POST</span> /whip</h3>
<p><strong>功能:</strong> WHIP推流端点，用于推送视频流到服务器</p></div>
<div class="endpoint"><h3><span class="method">POST</span> /whep/{stream_id}</h3>
<p><strong>功能:</strong> WHEP拉流端点，用于从服务器拉取视频流</p>
<p><strong>查询参数:</strong></p>
<table class="param-table">
<tr><th>参数</th><th>说明</th><th>示例</th></tr>
<tr><td><code>ai</code></td><td>启用AI处理（默认为pose）</td><td><code>?ai=true</code></td></tr>
<tr><td><code>transform</code></td><td>指定AI处理类型</td><td><code>?transform=edges</code></td></tr>
</table>
<p><strong>Transform类型:</strong></p>
<ul>
<li><code>pose</code> - YOLO姿态检测（默认）</li>
<li><code>edges</code> - 边缘检测</li>
<li><code>cartoon</code> - 卡通效果</li>
<li><code>rotate</code> - 旋转效果</li>
<li><code>none</code> - 无处理</li>
</ul>
<p><strong>使用示例:</strong></p>
<div class="example"># YOLO姿态检测
/whep/{stream_id}?ai=true
/whep/{stream_id}?transform=pose

# 边缘检测
/whep/{stream_id}?transform=edges

# 卡通效果
/whep/{stream_id}?transform=cartoon</div>
</div>
<div class="endpoint"><h3><span class="method">POST</span> /offer</h3>
<p><strong>功能:</strong> 双向通信端点，支持推流+拉流+AI处理</p></div>
</div></body></html>""", content_type='text/html')
        
        app.router.add_get("/api", serve_api_info)
        
        # 添加静态文件服务（用于测试官方示例）
        if os.path.exists('aiortc-main/examples/server/'):
            app.router.add_static('/examples', path='aiortc-main/examples/server/', name='examples')
        
        # 应用CORS到所有路由（排除静态文件路由）
        for route in list(app.router.routes()):
            if not route.resource.canonical.startswith('/examples'):
                cors.add(route)
        
        # 设置关闭时的处理函数
        app.on_shutdown.append(on_shutdown)
        
        # 创建并启动服务器
        logger.info(f"🌐 启动HTTP服务器 {args.host}:{args.port}")
        
        if args.cert_file and args.key_file:
            ssl_context = create_ssl_context(args.cert_file, args.key_file)
            runner = web.AppRunner(app)
            await runner.setup()
            server_runner = runner
            
            site = web.TCPSite(runner, args.host, args.port, ssl_context=ssl_context)
            await site.start()
            logger.info(f"✅ HTTPS服务器启动成功: https://{args.host}:{args.port}")
        else:
            runner = web.AppRunner(app)
            await runner.setup()
            server_runner = runner
            
            site = web.TCPSite(runner, args.host, args.port)
            await site.start()
            logger.info(f"✅ HTTP服务器启动成功: http://{args.host}:{args.port}")
        
        logger.info("🎯 服务器已就绪，等待连接...")
        logger.info(f"📺 Web界面: http://{args.host}:{args.port}/")
        
        # 保持服务器运行
        try:
            while True:
                await asyncio.sleep(3600)  # 每小时检查一次
        except asyncio.CancelledError:
            logger.info("🛑 收到停止信号")
            
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        await graceful_shutdown()
    except KeyboardInterrupt:
        logger.info("🛑 收到键盘中断")
        await graceful_shutdown()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="WHIP/WHEP WebRTC Server with AI Processing")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind")
    parser.add_argument("--port", type=int, default=8090, help="Port to bind")
    parser.add_argument("--cert-file", help="SSL certificate file (for HTTPS)")
    parser.add_argument("--key-file", help="SSL key file (for HTTPS)")
    parser.add_argument("--verbose", "-v", action="count")
    args = parser.parse_args()

    if args.verbose:
        logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        # 启用aiortc的详细日志
        logging.getLogger("aiortc").setLevel(logging.DEBUG)
        logging.getLogger("aioice").setLevel(logging.DEBUG) 
        logging.getLogger("aiohttp").setLevel(logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 设置ICE日志捕获
    setup_ice_log_capture()

    # 应用低延迟优化
    optimize_low_latency()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    if hasattr(signal, 'SIGBREAK'):  # Windows
        signal.signal(signal.SIGBREAK, signal_handler)

    try:
        # 运行服务器
        asyncio.run(run_server(args))
    except KeyboardInterrupt:
        logger.info("🛑 程序被用户中断")
    except Exception as e:
        logger.error(f"❌ 程序异常退出: {e}")
    finally:
        logger.info("🏁 程序结束") 