<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebRTC Player - 流播放器</title>
    <style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
    }

    .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        max-width: 1200px;
        margin: 0 auto 20px auto;
    }

    button {
        padding: 10px 20px;
        margin: 5px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
    }

    .primary-btn {
        background-color: #007bff;
        color: white;
    }

    .success-btn {
        background-color: #28a745;
        color: white;
    }

    .danger-btn {
        background-color: #dc3545;
        color: white;
    }

    .secondary-btn {
        background-color: #6c757d;
        color: white;
    }

    button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }

    .controls {
        margin: 20px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }

    .controls-row {
        display: flex;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
        margin: 10px 0;
    }

    input[type="text"], select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 200px;
        flex: 1;
    }

    video {
        width: 100%;
        max-width: 800px;
        border: 2px solid #ddd;
        border-radius: 8px;
        background: #000;
        margin: 10px 0;
    }

    .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        font-weight: bold;
    }

    .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    .log {
        background: #f8f9fa;
        padding: 10px;
        height: 200px;
        overflow-y: scroll;
        font-family: monospace;
        font-size: 12px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }

    .stream-list {
        background: #e7f3ff;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
        border: 1px solid #b3d7ff;
    }

    .stream-item {
        background: white;
        padding: 10px;
        margin: 5px 0;
        border-radius: 4px;
        border: 1px solid #ddd;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .stream-item:hover {
        background: #f0f8ff;
    }

    .stream-item.selected {
        background: #cce7ff;
        border-color: #007bff;
    }

    .transform-buttons {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        margin-top: 10px;
    }

    .transform-btn {
        padding: 5px 10px;
        font-size: 12px;
        border-radius: 3px;
        border: 1px solid #007bff;
        background: white;
        color: #007bff;
        cursor: pointer;
    }

    .transform-btn:hover {
        background: #007bff;
        color: white;
    }

    h1, h2, h3 {
        color: #333;
    }

    h2 {
        border-bottom: 2px solid #007bff;
        padding-bottom: 5px;
    }

    .info-text {
        font-size: 14px;
        color: #666;
        margin: 5px 0;
    }
    </style>
</head>
<body>

<div class="container">
    <h1>🎥 WebRTC 流播放器</h1>
    <p>专用的WHEP拉流播放器，支持AI视频处理效果</p>

    <div class="controls" style="background: #e8f4fd; border: 1px solid #bee5eb;">
        <h3>🚀 快速开始</h3>
        <p class="info-text">
            <strong>简单模式：</strong>点击"📡 获取可用流"自动填入第一个流，然后点击"▶️ 开始播放"<br>
            <strong>一键模式：</strong>点击"⚡ 快速连接"自动获取流并开始播放<br>
            <strong>高级模式：</strong>使用下方流列表选择特定流和AI效果
        </p>
    </div>
    
    <div class="controls">
        <h3>📡 服务器配置</h3>
        <div class="controls-row">
            <label>服务器地址:</label>
            <input type="text" id="serverUrl" value="http://localhost:8090" placeholder="http://localhost:8090">
            <button class="primary-btn" onclick="refreshStreams()">🔄 刷新流列表</button>
            <button class="secondary-btn" onclick="refreshStreams(true)">🎯 获取并自动选择第一个流</button>
        </div>
    </div>

    <div class="stream-list">
        <h3>📺 可用流列表</h3>
        <div id="streamsList">
            <p class="info-text">点击"刷新流列表"获取可用的流...</p>
        </div>
    </div>

    <div class="controls">
        <h3>🎮 播放控制</h3>
        <div class="controls-row">
            <label>流地址:</label>
            <input type="text" id="streamUrl" placeholder="选择上方流或手动输入WHEP URL">
        </div>
        <div class="controls-row">
            <button class="success-btn" onclick="startPlay()">▶️ 开始播放</button>
            <button class="danger-btn" onclick="stopPlay()">⏹️ 停止播放</button>
            <button class="secondary-btn" onclick="getStreams()">📡 获取可用流</button>
            <button class="primary-btn" onclick="quickConnect()">⚡ 快速连接</button>
            <button class="secondary-btn" onclick="clearLog()">🗑️ 清空日志</button>
        </div>
    </div>

    <div id="status" class="status info">准备就绪，请选择流进行播放</div>
</div>

<div class="container">
    <h2>📹 视频播放</h2>
    <video id="video" autoplay muted controls></video>
</div>

<div class="container">
    <h2>📋 连接日志</h2>
    <div id="log" class="log"></div>
</div>

<script>
    let pc = null;
    let video = document.getElementById('video');
    let currentStreams = [];
    
    function log(message) {
        const logDiv = document.getElementById('log');
        const timestamp = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${timestamp}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
        console.log(message);
    }
    
    function setStatus(message, type = 'info') {
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
    }
    
    async function refreshStreams(autoSelect = false) {
        try {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            if (!serverUrl) {
                setStatus('请输入服务器地址', 'error');
                return;
            }

            log('正在获取可用流列表...');
            setStatus('正在获取流列表...', 'info');

            const response = await fetch(`${serverUrl}/streams`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            currentStreams = data.streams;

            log(`找到 ${data.count} 个活跃流`);
            displayStreams(data.streams, data.available_transforms);

            if (data.count > 0) {
                setStatus(`找到 ${data.count} 个活跃流`, 'success');

                // 自动选择第一个流（类似test_whep.html的行为）
                if (autoSelect) {
                    const firstStream = data.streams[0];
                    document.getElementById('streamUrl').value = firstStream.whep_url;
                    log(`自动选择第一个流: ${firstStream.id}`);
                    setStatus(`已自动选择流: ${firstStream.id}`, 'success');

                    // 高亮第一个流
                    setTimeout(() => {
                        const firstStreamItem = document.querySelector('.stream-item');
                        if (firstStreamItem) {
                            document.querySelectorAll('.stream-item').forEach(item => {
                                item.classList.remove('selected');
                            });
                            firstStreamItem.classList.add('selected');
                        }
                    }, 100);
                }
            } else {
                setStatus('没有找到活跃流，请先开始推流', 'info');
            }

        } catch (error) {
            log(`获取流列表失败: ${error.message}`);
            setStatus(`获取流列表失败: ${error.message}`, 'error');
            document.getElementById('streamsList').innerHTML = '<p class="info-text">获取流列表失败，请检查服务器地址</p>';
        }
    }
    
    function displayStreams(streams, availableTransforms) {
        const container = document.getElementById('streamsList');
        
        if (streams.length === 0) {
            container.innerHTML = '<p class="info-text">没有找到活跃流，请先开始推流</p>';
            return;
        }
        
        let html = '';
        streams.forEach((stream, index) => {
            html += `
                <div class="stream-item" onclick="selectStream('${stream.id}', '${stream.whep_url}')">
                    <strong>流 ${index + 1}:</strong> ${stream.id}
                    <div class="info-text">基础拉流: ${stream.whep_url}</div>
                    <div class="transform-buttons">
                        <button class="transform-btn" onclick="event.stopPropagation(); selectStreamWithTransform('${stream.id}', 'none')">原始</button>
                        <button class="transform-btn" onclick="event.stopPropagation(); selectStreamWithTransform('${stream.id}', 'pose')">姿态检测</button>
                        <button class="transform-btn" onclick="event.stopPropagation(); selectStreamWithTransform('${stream.id}', 'edges')">边缘检测</button>
                        <button class="transform-btn" onclick="event.stopPropagation(); selectStreamWithTransform('${stream.id}', 'cartoon')">卡通效果</button>
                        <button class="transform-btn" onclick="event.stopPropagation(); selectStreamWithTransform('${stream.id}', 'rotate')">旋转</button>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    function selectStream(streamId, whepUrl) {
        document.getElementById('streamUrl').value = whepUrl;
        log(`已选择流: ${streamId}`);
        
        // 高亮选中的流
        document.querySelectorAll('.stream-item').forEach(item => {
            item.classList.remove('selected');
        });
        event.currentTarget.classList.add('selected');
    }
    
    function selectStreamWithTransform(streamId, transform) {
        const serverUrl = document.getElementById('serverUrl').value.trim();
        const whepUrl = `${serverUrl}/whep/${streamId}?transform=${transform}`;
        document.getElementById('streamUrl').value = whepUrl;
        log(`已选择流: ${streamId}，AI效果: ${transform}`);
    }

    // 类似test_whep.html的简单获取流功能
    async function getStreams() {
        try {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            if (!serverUrl) {
                setStatus('请输入服务器地址', 'error');
                return;
            }

            log('获取可用流列表...');
            const response = await fetch(`${serverUrl}/streams`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            log(`找到 ${data.count} 个活跃流`);

            if (data.streams.length > 0) {
                const stream = data.streams[0];
                document.getElementById('streamUrl').value = stream.whep_url;
                log(`自动填入第一个流: ${stream.id}`);
                setStatus(`找到 ${data.count} 个流，已自动填入`, 'success');

                // 同时更新流列表显示
                displayStreams(data.streams, data.available_transforms || []);

                // 高亮第一个流
                setTimeout(() => {
                    const firstStreamItem = document.querySelector('.stream-item');
                    if (firstStreamItem) {
                        document.querySelectorAll('.stream-item').forEach(item => {
                            item.classList.remove('selected');
                        });
                        firstStreamItem.classList.add('selected');
                    }
                }, 100);
            } else {
                setStatus('没有找到活跃流', 'error');
                document.getElementById('streamsList').innerHTML = '<p class="info-text">没有找到活跃流，请先开始推流</p>';
            }
        } catch (error) {
            log(`获取流列表失败: ${error.message}`);
            setStatus('获取流列表失败', 'error');
        }
    }

    // 快速连接功能：获取流并立即开始播放
    async function quickConnect() {
        try {
            log('🚀 开始快速连接...');
            setStatus('正在快速连接...', 'info');

            // 先获取流
            await getStreams();

            // 等待一下让UI更新
            await new Promise(resolve => setTimeout(resolve, 500));

            // 检查是否成功获取到流
            const streamUrl = document.getElementById('streamUrl').value.trim();
            if (streamUrl) {
                log('✅ 已获取流地址，开始播放...');
                await startPlay();
            } else {
                setStatus('快速连接失败：没有找到可用流', 'error');
            }
        } catch (error) {
            log(`快速连接失败: ${error.message}`);
            setStatus(`快速连接失败: ${error.message}`, 'error');
        }
    }

    async function startPlay() {
        const streamUrl = document.getElementById('streamUrl').value.trim();
        if (!streamUrl) {
            setStatus('请选择流或输入流地址', 'error');
            return;
        }

        try {
            log('开始WHEP拉流连接...');
            setStatus('正在建立连接...', 'info');

            // 创建PeerConnection
            pc = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.cloudflare.com:3478' },
                    { urls: 'stun:stun.l.google.com:19302' }
                ]
            });

            // 监听ICE连接状态
            pc.oniceconnectionstatechange = () => {
                log(`ICE连接状态: ${pc.iceConnectionState}`);
                if (pc.iceConnectionState === 'connected') {
                    setStatus('ICE连接成功', 'success');
                } else if (pc.iceConnectionState === 'failed') {
                    setStatus('ICE连接失败', 'error');
                }
            };

            // 监听连接状态
            pc.onconnectionstatechange = () => {
                log(`连接状态: ${pc.connectionState}`);
                if (pc.connectionState === 'connected') {
                    setStatus('连接成功，正在接收视频流...', 'success');
                } else if (pc.connectionState === 'failed') {
                    setStatus('连接失败', 'error');
                }
            };

            // 监听远程流
            pc.ontrack = (event) => {
                log(`收到远程轨道: ${event.track.kind}`);
                if (event.track.kind === 'video') {
                    video.srcObject = event.streams[0];
                    setStatus('视频流播放中', 'success');
                    log('视频流已连接并开始播放');
                }
            };

            // 创建offer
            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);
            log('已创建本地SDP offer');

            // 发送WHEP请求
            log(`发送WHEP请求到: ${streamUrl}`);
            const response = await fetch(streamUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/sdp'
                },
                body: offer.sdp
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const answerSdp = await response.text();
            log('收到服务器SDP answer');

            // 设置远程描述
            await pc.setRemoteDescription({
                type: 'answer',
                sdp: answerSdp
            });

            log('WHEP连接建立成功，等待媒体流...');

        } catch (error) {
            log(`WHEP连接失败: ${error.message}`);
            setStatus(`连接失败: ${error.message}`, 'error');
            if (pc) {
                pc.close();
                pc = null;
            }
        }
    }

    function stopPlay() {
        if (pc) {
            pc.close();
            pc = null;
            log('连接已关闭');
        }

        if (video.srcObject) {
            video.srcObject = null;
            log('视频流已清除');
        }

        setStatus('已停止播放', 'info');
    }

    function clearLog() {
        document.getElementById('log').innerHTML = '';
    }

    // 页面加载时自动获取流列表
    window.onload = () => {
        log('播放器页面加载完成');
        refreshStreams();
    };
</script>
</body>
</html>
