#!/usr/bin/env python3
"""
启动脚本 - 设置环境变量并启动WebRTC服务器
"""
import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_env_file(env_file=".env"):
    """加载.env文件中的环境变量"""
    env_path = Path(env_file)
    if not env_path.exists():
        logger.warning(f"环境变量文件 {env_file} 不存在")
        return False
    
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # 移除引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    os.environ[key] = value
                    logger.info(f"设置环境变量: {key}")
        
        logger.info(f"成功加载环境变量文件: {env_file}")
        return True
    except Exception as e:
        logger.error(f"加载环境变量文件失败: {e}")
        return False

def check_stun_turn_config():
    """检查STUN/TURN配置"""
    stun_servers = os.getenv('STUN_SERVERS', '')
    turn_servers = os.getenv('TURN_SERVERS', '')
    
    if not stun_servers and not turn_servers:
        logger.warning("⚠️  未配置STUN/TURN服务器")
        logger.warning("   服务器将使用空ICE配置，可能影响跨网络连接")
        logger.warning("   请参考 .env.example 文件配置STUN/TURN服务器")
        return False
    
    logger.info("✅ STUN/TURN配置检查通过")
    if stun_servers:
        logger.info(f"   STUN服务器已配置")
    if turn_servers:
        logger.info(f"   TURN服务器已配置")
    
    return True

def main():
    """主函数"""
    logger.info("🚀 启动WebRTC服务器...")
    
    # 加载环境变量
    env_loaded = load_env_file()
    if not env_loaded:
        logger.info("尝试加载 .env.example 作为参考...")
        load_env_file(".env.example")
    
    # 检查配置
    check_stun_turn_config()
    
    # 导入并启动服务器
    try:
        logger.info("正在启动服务器...")
        import whip_server_with_ai
        # 服务器会在导入时自动启动
    except ImportError as e:
        logger.error(f"导入服务器模块失败: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动服务器失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
