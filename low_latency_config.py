"""
低延迟WebRTC配置管理
协调客户端和服务器端的延迟和缓存设置
"""

import logging

logger = logging.getLogger(__name__)

class LowLatencyConfig:
    """低延迟配置管理类"""
    
    def __init__(self):
        # 基础配置
        self.enabled = True
        
        # 视频配置 - 平衡质量和稳定性，优化网络适应性
        self.video_config = {
            'width': 1280,
            'height': 720,
            'fps': 25,                  # 降低到25fps，减少丢帧风险
            'target_bitrate': 2000000,  # 降低目标码率到2.0Mbps，减少网络压力
            'min_bitrate': 800000,      # 降低最低码率到800kbps，应对弱网络
            'max_bitrate': 4000000,     # 降低最高码率到4.0Mbps，避免网络拥塞
        }

        # 服务器端缓冲区配置 - 极低延迟优化(防卡顿)
        self.server_jitter_buffer = {
            'video': {
                'capacity': 16,             # 视频缓冲区容量16帧(640ms@25fps) - 减少延迟
                'prefetch': 2,              # 预取2帧(80ms@25fps) - 最小预取
                'max_delay': 50,            # 最大延迟50ms
                'target_delay': 25,         # 目标延迟25ms
            },
            'audio': {
                'capacity': 8,              # 音频缓冲区容量8帧 - 减少音频延迟
                'prefetch': 1,              # 预取1帧 - 最小预取
                'max_delay': 25,            # 最大延迟25ms
                'target_delay': 10,         # 目标延迟10ms
            }
        }
        
        # 编码器配置 - 中等质量，提高容错性
        self.encoder_config = {
            'h264': {
                'preset': 'ultrafast',      # 最快预设，最低编码延迟
                'tune': 'zerolatency',      # 零延迟调优，禁用B帧和前瞻
                'profile': 'baseline',      # 基线配置，最低解码延迟
                'level': '3.1',             # 适合720p的级别
                'profile_level_id': '42E01F',  # 匹配aiortc默认的profile-level-id
                'packetization_mode': '1',  # 匹配客户端的packetization-mode
                'level_asymmetry_allowed': '1',  # 允许级别不对称
                'keyint': '75',             # 关键帧间隔3秒@25fps，减少编码负载
                'min_keyint': '50',         # 最小关键帧间隔2秒
                'bframes': '0',             # 禁用B帧，减少延迟
                'refs': '1',                # 最少参考帧，减少延迟
                'rc_lookahead': '0',        # 禁用前瞻，减少延迟
                'subme': '1',               # 最快子像素估计
                'me': 'dia',                # 最快运动估计算法
                'trellis': '0',             # 禁用网格量化，减少延迟
            },
            'vp8': {
                # VP8已在客户端完全禁用，但保留配置以备用
                'deadline': 'realtime',     # 实时编码
                'cpu_used': '8',            # 最快编码速度
                'static_thresh': '0',       # 禁用静态阈值
                'max_intra_rate': '300',    # 适配30fps的关键帧频率
                'kf_max_dist': '30',        # 关键帧最大距离1秒
                'kf_min_dist': '15',        # 关键帧最小距离0.5秒
            }
        }
        
        # 客户端配置 - TURN中继超低延迟优化
        self.client_config = {
            'ice_connection_timeout': 8000,        # ICE连接超时8秒（TURN连接更快）
            'ice_backup_ping_interval': 1000,      # 增加ping频率，快速检测连接
            'ice_inactive_timeout': 6000,          # 减少非活跃超时，快速重连
            'enable_hardware_encoder': True,
            'enable_h264_high_profile': False,     # 禁用高配置，减少延迟
            'enable_intel_vp8': False,             # 禁用Intel VP8
            # 25fps配置，超低延迟优化
            'target_fps': 25,                      # 与服务器端统一25fps
            'capture_fps_tolerance': 2,            # 严格fps控制，23-27fps
            'frame_drop_threshold': 0.02,          # 降低到2%丢帧阈值，激进丢帧
        }
        
        # 网络配置 - TURN中继超低延迟优化
        self.network_config = {
            'bundle_policy': 'max-bundle',          # 最大化bundle策略
            'rtcp_mux_policy': 'require',           # 要求RTCP复用
            'tcp_candidate_policy': 'disabled',    # 禁用TCP候选，强制UDP减少延迟
            'continual_gathering_policy': 'gather_once',  # 单次收集，减少延迟
            'ice_restart_policy': 'normal',        # 正常ICE重启策略
            'dtls_srtp_key_agreement': 'enabled',  # 启用DTLS-SRTP密钥协商
        }
    
    def get_server_jitter_buffer_config(self):
        """获取服务器端JitterBuffer配置"""
        return {
            'video': {
                'capacity': self.server_jitter_buffer['video']['capacity'],
                'prefetch': self.server_jitter_buffer['video']['prefetch'],
                'is_video': True
            },
            'audio': {
                'capacity': self.server_jitter_buffer['audio']['capacity'],
                'prefetch': self.server_jitter_buffer['audio']['prefetch'],
                'is_video': False
            }
        }
    
    def get_h264_encoder_options(self):
        """获取H264编码器选项"""
        return self.encoder_config.get('h264', {})

    def get_vp8_encoder_options(self):
        """获取VP8编码器选项"""
        return self.encoder_config.get('vp8', {})
    
    def get_client_config_json(self):
        """获取客户端配置JSON（用于API返回）"""
        return {
            'lowLatencyEnabled': self.enabled,
            'video': self.video_config,
            'client': self.client_config,
            'network': self.network_config
        }
    
    def log_configuration(self):
        """记录当前配置"""
        if self.enabled:
            logger.info("⚡ TURN中继超低延迟模式已启用")
        else:
            logger.info("📺 标准延迟模式")

# 全局配置实例
low_latency_config = LowLatencyConfig()

def get_low_latency_config():
    """获取低延迟配置实例"""
    return low_latency_config

def apply_server_optimizations():
    """应用服务器端优化"""
    config = get_low_latency_config()
    
    if not config.enabled:
        return
    
    # 这个函数将被主服务器调用来应用优化
    logger.info("🔧 应用服务器端低延迟优化...")
    
    # 返回配置供服务器使用
    return {
        'jitter_buffer': config.get_server_jitter_buffer_config(),
        'h264_options': config.get_h264_encoder_options(),
        'vp8_options': config.get_vp8_encoder_options()
    }

def get_client_optimization_config():
    """获取客户端优化配置"""
    config = get_low_latency_config()
    return config.get_client_config_json()
